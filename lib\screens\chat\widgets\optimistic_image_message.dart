import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/utils/cache_manager.dart';

/// Optimistic image message widget for WhatsApp-like experience
/// Shows local images immediately while uploading in background
class OptimisticImageMessage extends StatefulWidget {
  final Message message;
  final bool isMe;
  final VoidCallback? onTap;
  final VoidCallback? onRetry;
  final double maxWidth;
  final double maxHeight;

  const OptimisticImageMessage({
    super.key,
    required this.message,
    required this.isMe,
    this.onTap,
    this.onRetry,
    this.maxWidth = 250,
    this.maxHeight = 300,
  });

  @override
  State<OptimisticImageMessage> createState() => _OptimisticImageMessageState();
}

class _OptimisticImageMessageState extends State<OptimisticImageMessage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _imageLoaded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: widget.maxWidth,
          maxHeight: widget.maxHeight,
        ),
        child: Stack(
          children: [
            // Main image
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: _buildImageWidget(),
            ),
            // Upload status overlay
            if (widget.message.isUploading ||
                widget.message.isPendingUpload ||
                widget.message.isUploadFailed)
              _buildStatusOverlay(),
            // Retry button for failed uploads
            if (widget.message.isUploadFailed && widget.onRetry != null)
              _buildRetryButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildImageWidget() {
    // Try to show local image first, then fallback to remote
    if (widget.message.hasLocalImage) {
      return _buildLocalImage();
    } else if (widget.message.mediaUrl != null) {
      return _buildRemoteImage();
    } else {
      return _buildPlaceholder();
    }
  }

  Widget _buildLocalImage() {
    return GestureDetector(
      onTap: widget.onTap,
      child: Image.file(
        File(widget.message.localImagePath!),
        fit: BoxFit.cover,
        width: widget.maxWidth,
        height: widget.maxHeight,
        errorBuilder: (context, error, stackTrace) {
          // If local image fails, try remote
          if (widget.message.mediaUrl != null) {
            return _buildRemoteImage();
          }
          return _buildErrorWidget();
        },
        frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
          if (wasSynchronouslyLoaded) {
            _imageLoaded = true;
            return child;
          }
          if (frame != null) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted && !_imageLoaded) {
                setState(() {
                  _imageLoaded = true;
                });
              }
            });
            return child;
          }
          return _buildLoadingWidget();
        },
      ),
    );
  }

  Widget _buildRemoteImage() {
    return GestureDetector(
      onTap: widget.onTap,
      child: CachedNetworkImage(
        imageUrl: widget.message.mediaUrl!,
        cacheManager: CustomCacheManager.chatInstance,
        fit: BoxFit.cover,
        width: widget.maxWidth,
        height: widget.maxHeight,
        placeholder: (context, url) => _buildLoadingWidget(),
        errorWidget: (context, url, error) => _buildErrorWidget(),
        fadeInDuration: const Duration(milliseconds: 200),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      width: widget.maxWidth,
      height: widget.maxHeight,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: widget.maxWidth,
      height: widget.maxHeight,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.broken_image, color: Colors.grey, size: 32),
            SizedBox(height: 4),
            Text(
              'Image not available',
              style: TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: widget.maxWidth,
      height: widget.maxHeight,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Icon(Icons.image, color: Colors.grey, size: 48),
      ),
    );
  }

  Widget _buildStatusOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(child: _buildStatusWidget()),
      ),
    );
  }

  Widget _buildStatusWidget() {
    if (widget.message.isUploading) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            value: widget.message.uploadProgress,
            strokeWidth: 3,
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            backgroundColor: Colors.white.withOpacity(0.3),
          ),
          const SizedBox(height: 8),
          Text(
            widget.message.uploadProgress != null
                ? '${widget.message.uploadProgressPercentage}%'
                : 'Uploading...',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    } else if (widget.message.isPendingUpload) {
      return const Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.schedule, color: Colors.white, size: 24),
          SizedBox(height: 4),
          Text(
            'Pending',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    } else if (widget.message.isUploadFailed) {
      return const Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.error, color: Colors.red, size: 24),
          SizedBox(height: 4),
          Text(
            'Failed',
            style: TextStyle(
              color: Colors.red,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildRetryButton() {
    return Positioned(
      bottom: 8,
      right: 8,
      child: GestureDetector(
        onTap: widget.onRetry,
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Icon(Icons.refresh, color: Colors.white, size: 16),
        ),
      ), 
    );
  }
}
