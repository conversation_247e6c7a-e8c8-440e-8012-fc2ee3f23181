import 'package:cloud_firestore/cloud_firestore.dart';

enum MessageType {
  text,
  image,
  file,
  audio,
  video,
  location,
  contact,
  order,
  catalog,
}

enum MessageStatus { sending, sent, delivered, read, failed }

/// Enhanced message status for optimistic UI
enum OptimisticMessageStatus {
  pending, // Message created locally, not yet uploaded
  uploading, // Currently uploading to server
  sent, // Successfully sent to server
  delivered, // Delivered to recipient
  read, // Read by recipient
  failed, // Failed to send
}

/// Upload status for background uploads
enum UploadStatus { pending, uploading, completed, failed, cancelled }

class Message {
  final String id;
  final String senderId;
  final String senderName;
  final String? senderProfileUrl;
  final MessageType type;
  final String? text;
  final String? mediaUrl;
  final String? fileName;
  final int? fileSize;
  final String? thumbnailUrl;
  final MessageStatus status;
  final DateTime timestamp;
  final String? replyToMessageId;
  final String? replyToText;
  final String? replyToSenderName;
  final bool isForwarded;
  final List<String> readBy;
  final List<String> deletedBy; // Track which users have deleted this message
  final Map<String, dynamic>? metadata; // For order, catalog, location data

  // Local storage fields for WhatsApp-like functionality
  final String? localImagePath; // Path to locally stored image
  final String? localThumbnailPath; // Path to locally stored thumbnail
  final OptimisticMessageStatus? optimisticStatus; // Status for optimistic UI
  final UploadStatus? uploadStatus; // Upload progress status
  final double? uploadProgress; // Upload progress (0.0 to 1.0)
  final bool isLocalOnly; // True if message exists only locally

  Message({
    required this.id,
    required this.senderId,
    required this.senderName,
    this.senderProfileUrl,
    required this.type,
    this.text,
    this.mediaUrl,
    this.fileName,
    this.fileSize,
    this.thumbnailUrl,
    this.status = MessageStatus.sending,
    required this.timestamp,
    this.replyToMessageId,
    this.replyToText,
    this.replyToSenderName,
    this.isForwarded = false,
    this.readBy = const [],
    this.deletedBy = const [],
    this.metadata,
    // Local storage fields
    this.localImagePath,
    this.localThumbnailPath,
    this.optimisticStatus,
    this.uploadStatus,
    this.uploadProgress,
    this.isLocalOnly = false,
  });

  // Convert Message to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'senderProfileUrl': senderProfileUrl,
      'type': type.name,
      'text': text,
      'mediaUrl': mediaUrl,
      'fileName': fileName,
      'fileSize': fileSize,
      'thumbnailUrl': thumbnailUrl,
      'status': status.name,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'replyToMessageId': replyToMessageId,
      'replyToText': replyToText,
      'replyToSenderName': replyToSenderName,
      'isForwarded': isForwarded,
      'readBy': readBy,
      'deletedBy': deletedBy,
      'metadata': metadata,
      // Note: Local storage fields are not stored in Firestore
    };
  }

  // Create Message from Firestore document
  factory Message.fromMap(Map<String, dynamic> map) {
    return Message(
      id: map['id'] ?? '',
      senderId: map['senderId'] ?? '',
      senderName: map['senderName'] ?? '',
      senderProfileUrl: map['senderProfileUrl'],
      type: MessageType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => MessageType.text,
      ),
      text: map['text'],
      mediaUrl: map['mediaUrl'],
      fileName: map['fileName'],
      fileSize: map['fileSize'],
      thumbnailUrl: map['thumbnailUrl'],
      status: MessageStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => MessageStatus.sent,
      ),
      timestamp: _parseDateTime(map['timestamp']),
      replyToMessageId: map['replyToMessageId'],
      replyToText: map['replyToText'],
      replyToSenderName: map['replyToSenderName'],
      isForwarded: map['isForwarded'] ?? false,
      readBy: List<String>.from(map['readBy'] ?? []),
      deletedBy: List<String>.from(map['deletedBy'] ?? []),
      metadata: map['metadata'],
    );
  }

  // Helper method to parse DateTime from various formats
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) {
      return DateTime.now();
    }

    if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    }

    if (value is Timestamp) {
      return value.toDate();
    }

    if (value is DateTime) {
      return value;
    }

    // Fallback to current time if we can't parse
    return DateTime.now();
  }

  // Create Message from Firestore DocumentSnapshot
  factory Message.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Message.fromMap(data);
  }

  // Copy with method for updating message data
  Message copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? senderProfileUrl,
    MessageType? type,
    String? text,
    String? mediaUrl,
    String? fileName,
    int? fileSize,
    String? thumbnailUrl,
    MessageStatus? status,
    DateTime? timestamp,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    bool? isForwarded,
    List<String>? readBy,
    List<String>? deletedBy,
    Map<String, dynamic>? metadata,
    // Local storage fields
    String? localImagePath,
    String? localThumbnailPath,
    OptimisticMessageStatus? optimisticStatus,
    UploadStatus? uploadStatus,
    double? uploadProgress,
    bool? isLocalOnly,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderProfileUrl: senderProfileUrl ?? this.senderProfileUrl,
      type: type ?? this.type,
      text: text ?? this.text,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      replyToText: replyToText ?? this.replyToText,
      replyToSenderName: replyToSenderName ?? this.replyToSenderName,
      isForwarded: isForwarded ?? this.isForwarded,
      readBy: readBy ?? this.readBy,
      deletedBy: deletedBy ?? this.deletedBy,
      metadata: metadata ?? this.metadata,
      // Local storage fields
      localImagePath: localImagePath ?? this.localImagePath,
      localThumbnailPath: localThumbnailPath ?? this.localThumbnailPath,
      optimisticStatus: optimisticStatus ?? this.optimisticStatus,
      uploadStatus: uploadStatus ?? this.uploadStatus,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      isLocalOnly: isLocalOnly ?? this.isLocalOnly,
    );
  }

  // Check if message is sent by current user
  bool isSentByMe(String currentUserId) {
    return senderId == currentUserId;
  }

  // Check if message is read by specific user
  bool isReadBy(String userId) {
    return readBy.contains(userId);
  }

  // Check if message is deleted by specific user
  bool isDeletedBy(String userId) {
    return deletedBy.contains(userId);
  }

  // Helper methods for local storage functionality

  /// Check if message has local image available
  bool get hasLocalImage {
    return localImagePath != null && localImagePath!.isNotEmpty;
  }

  /// Check if message has local thumbnail available
  bool get hasLocalThumbnail {
    return localThumbnailPath != null && localThumbnailPath!.isNotEmpty;
  }

  /// Get the best available image path (local first, then remote)
  String? get bestImagePath {
    if (hasLocalImage) return localImagePath;
    return mediaUrl;
  }

  /// Get the best available thumbnail path (local first, then remote)
  String? get bestThumbnailPath {
    if (hasLocalThumbnail) return localThumbnailPath;
    return thumbnailUrl;
  }

  /// Check if message is currently uploading
  bool get isUploading {
    return uploadStatus == UploadStatus.uploading ||
        optimisticStatus == OptimisticMessageStatus.uploading;
  }

  /// Check if message upload failed
  bool get isUploadFailed {
    return uploadStatus == UploadStatus.failed ||
        optimisticStatus == OptimisticMessageStatus.failed;
  }

  /// Check if message is pending upload
  bool get isPendingUpload {
    return uploadStatus == UploadStatus.pending ||
        optimisticStatus == OptimisticMessageStatus.pending;
  }

  /// Get upload progress percentage (0-100)
  int get uploadProgressPercentage {
    if (uploadProgress == null) return 0;
    return (uploadProgress! * 100).round();
  }

  @override
  String toString() {
    return 'Message(id: $id, senderId: $senderId, type: $type, text: $text, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
