name: mr_garments_mobile
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.3.6+22

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  google_fonts: ^6.2.1
  lucide_icons: ^0.257.0
  flutter_native_splash: ^2.4.6
  image_picker: ^1.1.2
  change_app_package_name: ^1.5.0
  http: ^1.4.0
  flutter_riverpod: ^2.6.1
  shared_preferences: ^2.5.3
  intl: ^0.20.2
  http_parser: ^4.1.2
  path: ^1.9.1
  share_plus: ^11.0.0
  path_provider: ^2.1.5
  flutter_image_compress: ^2.4.0
  firebase_auth: ^5.6.2
  firebase_core: ^3.15.1
  cloud_firestore: ^5.6.11
  firebase_storage: ^12.4.9
  firebase_messaging: ^15.2.9
  file_picker: ^10.2.0
  flutter_local_notifications: ^19.3.1
  flutter_secure_storage: ^9.2.4
  url_launcher: ^6.3.2
  cached_network_image: ^3.4.1
  flutter_cache_manager: ^3.4.1
  photo_view: ^0.15.0


flutter_native_splash:
  color: "#F8F8F8"
  image: assets/images/splash_logo.png #  Center logo 
  branding: assets/images/Braincave1.png # Bottom image 
  branding_mode: bottom
  android_gravity: center
  ios_content_mode: center
  color_dark: "#F8F8F8"
  fullscreen: true

  android_12:
    color: "#F8F8F8"
    image: assets/images/splash_logo.png
    branding: assets/images/Braincave1.png
    branding_mode: bottom

  android: true
  ios: true
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/brands/
    - assets/images/categories/
    - assets/images/catalogs/


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
